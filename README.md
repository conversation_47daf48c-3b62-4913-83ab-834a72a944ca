# Secret Message Decoder

This Python script decodes a secret message by fetching coordinate data from a Google Docs URL and plotting characters on a grid to reveal hidden text.

## Features

- Fetches data directly from a Google Docs URL (no manual copy-paste required)
- Parses coordinate data from HTML content
- Creates a visual grid representation
- Displays the message in multiple formats for better readability
- Provides character analysis and grid statistics

## Requirements

- Python 3.6+
- requests library

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Simply run the script:
```bash
python decode_secret_message.py
```

The script will:
1. Fetch the coordinate data from the Google Docs URL
2. Parse the x-coordinate, character, and y-coordinate data
3. Create a grid and plot the characters
4. Display the decoded message in multiple formats

## Output

The script provides:
- Character analysis (count of each character type)
- Grid dimensions and coordinate ranges
- Original message with Unicode block characters (█ and ░)
- Enhanced readability version using # and . characters
- Interpreted text message

## Example Output

```
Found 350 coordinate points
Character analysis:
  '░': 85 occurrences
  '█': 265 occurrences

Grid dimensions: 94 x 7
X range: 0 to 93
Y range: 0 to 6

Decoded message (enhanced readability):
########.     ########.   ##########.    #######.  ##.           ###. ###.    ###. ##.     ##.
##.     ##. ###.     ###. ##.          ###.    ##. ###.   ###.   ##.    ##.  ##.   ##.     ##.
##.     ##. ##.       ##. ##.         ###.          ##.  #####. ###.     ##.##.    ##.     ##.
########.   ##.       ##. ########.   ##.           ###. ##.##. ##.       ###.     ##########.
##.     ##. ##.       ##. ##.         ###.           ##.##. ##.##.       ##.##.    ##.     ##.
##.     ##. ###.     ###. ##.          ###.    ##.   ####.   ####.      ##.  ##.   ##.     ##.
########.     ########.   ##########.    #######.     ##.     ##.     ###.    ###. ##.     ##.

Interpreted message: ROCKWELL
```

## How It Works

1. **Data Fetching**: Uses the `requests` library to fetch HTML content from the Google Docs URL
2. **Parsing**: Uses regular expressions to extract coordinate triplets (x, character, y) from the HTML
3. **Grid Creation**: Maps coordinates to characters in a dictionary structure
4. **Visualization**: Renders the grid with characters positioned according to their coordinates
5. **Message Extraction**: The filled blocks (█ or #) form letters that spell out the secret message

## Data Format

The input data consists of rows with three columns:
- **x-coordinate**: Horizontal position (0-93)
- **Character**: Either █ (filled block) or ░ (empty block)
- **y-coordinate**: Vertical position (0-6)

The script automatically handles the coordinate system and displays the message with y=6 at the top and y=0 at the bottom for proper text orientation.
