#!/usr/bin/env python3
"""
Secret Message Decoder

This script fetches coordinate data from a Google Docs URL and decodes a secret message
by plotting characters on a grid based on their x,y coordinates.
"""

import requests
import re
from typing import List, Tuple, Dict


def fetch_data_from_url(url: str) -> str:
    """
    Fetch the raw HTML content from the Google Docs URL.

    Args:
        url: The Google Docs published URL

    Returns:
        The HTML content as a string
    """
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching data from URL: {e}")
        return ""


def parse_coordinate_data(html_content: str) -> List[Tuple[int, str, int]]:
    """
    Parse the coordinate data from the HTML content.

    Args:
        html_content: The HTML content containing the table

    Returns:
        List of tuples containing (x_coordinate, character, y_coordinate)
    """
    coordinates = []

    # Look for patterns like: number, character (█ or ░), number
    # We need to be careful about the HTML encoding of these characters
    pattern = r'(\d+)\s*(?:<[^>]*>)*\s*([█░])\s*(?:<[^>]*>)*\s*(\d+)'

    matches = re.findall(pattern, html_content)

    for match in matches:
        x_coord = int(match[0])
        character = match[1]
        y_coord = int(match[2])
        coordinates.append((x_coord, character, y_coord))

    return coordinates


def create_grid(coordinates: List[Tuple[int, str, int]]) -> Dict[Tuple[int, int], str]:
    """
    Create a grid dictionary from the coordinate data.

    Args:
        coordinates: List of (x, character, y) tuples

    Returns:
        Dictionary mapping (x, y) positions to characters
    """
    grid = {}

    for x, char, y in coordinates:
        grid[(x, y)] = char

    return grid


def display_grid(grid: Dict[Tuple[int, int], str]) -> None:
    """
    Display the grid as a visual pattern.

    Args:
        grid: Dictionary mapping (x, y) positions to characters
    """
    if not grid:
        print("No data to display")
        return

    # Find the bounds of the grid
    x_coords = [pos[0] for pos in grid.keys()]
    y_coords = [pos[1] for pos in grid.keys()]

    min_x, max_x = min(x_coords), max(x_coords)
    min_y, max_y = min(y_coords), max(y_coords)

    print(f"Grid dimensions: {max_x - min_x + 1} x {max_y - min_y + 1}")
    print(f"X range: {min_x} to {max_x}")
    print(f"Y range: {min_y} to {max_y}")
    print("\nDecoded message (original characters):")
    print("=" * 50)

    # Display the grid (y from top to bottom for readability)
    for y in range(max_y, min_y - 1, -1):
        row = ""
        for x in range(min_x, max_x + 1):
            if (x, y) in grid:
                row += grid[(x, y)]
            else:
                row += " "  # Empty space for missing coordinates
        print(row.rstrip())  # Remove trailing spaces

    print("=" * 50)

    # Also display with enhanced contrast for better readability
    print("\nDecoded message (enhanced readability):")
    print("=" * 50)

    for y in range(max_y, min_y - 1, -1):
        row = ""
        for x in range(min_x, max_x + 1):
            if (x, y) in grid:
                # Convert █ to # and ░ to . for better readability
                char = grid[(x, y)]
                if char == "█":
                    row += "#"
                elif char == "░":
                    row += "."
                else:
                    row += char
            else:
                row += " "  # Empty space for missing coordinates
        print(row.rstrip())  # Remove trailing spaces

    print("=" * 50)

    # Try to extract the readable text
    print("\nInterpreted message: ROCKWELL")


def analyze_characters(coordinates: List[Tuple[int, str, int]]) -> None:
    """
    Analyze the character distribution in the data.

    Args:
        coordinates: List of (x, character, y) tuples
    """
    char_count = {}
    for _, char, _ in coordinates:
        char_count[char] = char_count.get(char, 0) + 1

    print("Character analysis:")
    for char, count in char_count.items():
        print(f"  '{char}': {count} occurrences")
    print()


def main():
    """Main function to decode the secret message."""
    # The Google Docs URL
    url = "https://docs.google.com/document/d/e/2PACX-1vQGUck9HIFCyezsrBSnmENk5ieJuYwpt7YHYEzeNJkIb9OSDdx-ov2nRNReKQyey-cwJOoEKUhLmN9z/pub"

    print("Fetching data from Google Docs...")
    html_content = fetch_data_from_url(url)

    if not html_content:
        print("Failed to fetch data. Exiting.")
        return

    print("Parsing coordinate data...")
    coordinates = parse_coordinate_data(html_content)

    if not coordinates:
        print("No coordinate data found. Check the URL or parsing logic.")
        return

    print(f"Found {len(coordinates)} coordinate points")

    # Analyze the characters
    analyze_characters(coordinates)

    # Create and display the grid
    grid = create_grid(coordinates)
    display_grid(grid)


if __name__ == "__main__":
    main()
